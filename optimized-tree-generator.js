/**
 * 优化的文件树生成器
 * 特性：当多级目录下面只有一个文件时，将这些目录合并显示
 * 基于 computeAndStringifyDirectoryTree 实现
 */

/**
 * 从文件路径列表生成优化的树形结构
 * @param {string[]} filePaths - 文件路径数组
 * @param {string} rootName - 可选的根目录名称
 * @returns {string} 树形结构字符串
 */
function generateOptimizedFileTree(filePaths, rootName = null) {
    if (!filePaths || filePaths.length === 0) {
        return 'No files provided';
    }

    // 构建树结构
    const tree = buildTree(filePaths);
    
    // 自动检测根目录名称
    if (!rootName && filePaths.length > 0) {
        const firstPath = filePaths[0];
        const parts = firstPath.split('/').filter(part => part.length > 0);
        rootName = parts.length > 0 ? parts[0] + '/' : 'root/';
    }

    // 转换为字符串
    let result = rootName + '\n';
    const entries = Object.keys(tree).sort();
    
    // 如果只有一个根目录，跳过它并显示其子项
    if (entries.length === 1 && tree[entries[0]].isDirectory) {
        const rootDir = entries[0];
        const children = tree[rootDir].children;
        result += renderOptimizedChildren(children, '');
    } else {
        result += renderOptimizedChildren(tree, '');
    }
    
    return result;
}

/**
 * 构建树结构
 * @param {string[]} filePaths - 文件路径数组
 * @returns {Object} 树结构对象
 */
function buildTree(filePaths) {
    const tree = {};
    
    filePaths.forEach(filePath => {
        const parts = filePath.split('/').filter(part => part.length > 0);
        let current = tree;
        
        parts.forEach((part, index) => {
            if (!current[part]) {
                current[part] = {
                    isDirectory: index < parts.length - 1,
                    children: {}
                };
            }
            current = current[part].children;
        });
    });
    
    return tree;
}

/**
 * 检查目录是否应该被合并（只有一个子项且该子项也是目录）
 * @param {Object} node - 目录节点
 * @returns {boolean} 是否应该合并
 */
function shouldCollapse(node) {
    if (!node.isDirectory) return false;
    
    const childKeys = Object.keys(node.children);
    if (childKeys.length !== 1) return false;
    
    const onlyChild = node.children[childKeys[0]];
    return onlyChild.isDirectory;
}

/**
 * 获取合并后的路径
 * @param {Object} node - 起始目录节点
 * @param {string} currentPath - 当前累积路径
 * @returns {Object} 包含 collapsedPath 和 finalNode 的对象
 */
function getCollapsedPath(node, currentPath = '') {
    if (!node.isDirectory) {
        return { collapsedPath: currentPath, finalNode: node };
    }
    
    const childKeys = Object.keys(node.children);
    if (childKeys.length !== 1) {
        return { collapsedPath: currentPath, finalNode: node };
    }
    
    const childName = childKeys[0];
    const child = node.children[childName];
    const newPath = currentPath ? `${currentPath}/${childName}` : childName;
    
    if (child.isDirectory && shouldCollapse(child)) {
        return getCollapsedPath(child, newPath);
    } else {
        return { collapsedPath: newPath, finalNode: child };
    }
}

/**
 * 渲染优化的子项（带目录合并功能）
 * @param {Object} children - 子项对象
 * @param {string} prefix - 当前前缀
 * @returns {string} 格式化字符串
 */
function renderOptimizedChildren(children, prefix) {
    let result = '';
    const entries = Object.keys(children).sort();
    
    entries.forEach((name, index) => {
        const isLast = index === entries.length - 1;
        const node = children[name];
        
        const branchSymbol = isLast ? '└── ' : '├── ';
        
        // 检查此目录是否应该被合并
        if (node.isDirectory && shouldCollapse(node)) {
            const { collapsedPath, finalNode } = getCollapsedPath(node, name);
            const displayName = finalNode.isDirectory ? `${collapsedPath}/` : collapsedPath;
            
            result += `${prefix}${branchSymbol}${displayName}\n`;
            
            // 如果最终节点是有子项的目录，渲染它们
            if (finalNode.isDirectory && Object.keys(finalNode.children).length > 0) {
                const nextPrefix = prefix + (isLast ? '    ' : '│   ');
                result += renderOptimizedChildren(finalNode.children, nextPrefix);
            }
        } else {
            // 正常渲染
            const displayName = node.isDirectory ? `${name}/` : name;
            result += `${prefix}${branchSymbol}${displayName}\n`;
            
            if (node.isDirectory && Object.keys(node.children).length > 0) {
                const nextPrefix = prefix + (isLast ? '    ' : '│   ');
                result += renderOptimizedChildren(node.children, nextPrefix);
            }
        }
    });
    
    return result;
}

// ==================== 测试用例 ====================

console.log('🌳 优化的文件树生成器测试');
console.log('=' .repeat(50));

// 您的原始文件路径
const originalPaths = [
    'javascripts/service/dashboard/dashboard_service.ts',
    'javascripts/constants/cookies.ts',
    'javascripts/groups/DashboardGroupsFilter.vue',
    'javascripts/locales/en/dashboard/groups.js'
];

console.log('📁 原始文件路径：');
console.log(generateOptimizedFileTree(originalPaths));

// 更复杂的测试用例
const complexPaths = [
    'src/components/ui/Button.jsx',
    'src/components/ui/Input.jsx',
    'src/utils/helpers/string.js',
    'src/utils/helpers/date.js',
    'src/pages/home/<USER>',
    'src/assets/images/logo.png',
    'src/deep/nested/single/path/file.txt',
    'src/another/deep/single/chain/config.json',
    'public/favicon.ico',
    'docs/readme.md',
    'package.json'
];

console.log('📁 复杂目录结构测试：');
console.log(generateOptimizedFileTree(complexPaths, 'my-project/'));

// 导出函数供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        generateOptimizedFileTree,
        buildTree,
        shouldCollapse,
        getCollapsedPath,
        renderOptimizedChildren
    };
}
