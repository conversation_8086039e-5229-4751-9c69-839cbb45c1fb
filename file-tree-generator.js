/**
 * Generate a tree structure from a list of file paths
 * Based on the computeAndStringifyDirectoryTree implementation
 */

/**
 * Build a tree structure from file paths
 * @param {string[]} filePaths - Array of file paths
 * @returns {Object} Tree structure object
 */
function buildTreeFromPaths(filePaths) {
    const tree = {};

    filePaths.forEach(filePath => {
        const parts = filePath.split('/').filter(part => part.length > 0);
        let current = tree;

        parts.forEach((part, index) => {
            if (!current[part]) {
                current[part] = {
                    isDirectory: index < parts.length - 1,
                    children: {}
                };
            }
            current = current[part].children;
        });
    });

    return tree;
}

/**
 * Convert tree structure to string representation
 * @param {Object} tree - Tree structure object
 * @param {string} prefix - Current prefix for tree formatting
 * @param {boolean} isRoot - Whether this is the root level
 * @param {boolean} showRootPath - Whether to show root path
 * @returns {string} String representation of the tree
 */
function stringifyTree(tree, prefix = '', isRoot = true, showRootPath = true) {
    let result = '';
    const entries = Object.keys(tree).sort();

    // Show root path only once at the very beginning
    if (isRoot && showRootPath) {
        result += 'javascripts/\n';
    }

    entries.forEach((name, index) => {
        const isLast = index === entries.length - 1;
        const node = tree[name];

        // Skip the root 'javascripts' directory in the tree structure
        if (isRoot && name === 'javascripts') {
            result += stringifyTree(node.children, '', false, false);
            return;
        }

        // Create the tree branch symbols
        const branchSymbol = isLast ? '└── ' : '├── ';
        const displayName = node.isDirectory ? `${name}/` : name;

        result += `${prefix}${branchSymbol}${displayName}\n`;

        // Process children if it's a directory
        if (node.isDirectory && Object.keys(node.children).length > 0) {
            const nextPrefix = prefix + (isLast ? '    ' : '│   ');
            result += stringifyTree(node.children, nextPrefix, false, false);
        }
    });

    return result;
}

/**
 * Determine the root path from the file paths
 * @param {string[]} entries - Directory entries
 * @param {Object} tree - Tree structure
 * @returns {string} Root path
 */
function getRootPath(entries, tree) {
    // Find common root directory
    if (entries.length === 0) return '';

    // For this example, we'll use a generic root
    return 'javascripts/';
}

/**
 * Main function to generate tree structure from file paths
 * @param {string[]} filePaths - Array of file paths
 * @param {string} rootName - Optional root directory name (auto-detected if not provided)
 * @returns {string} Tree structure as string
 */
function generateFileTree(filePaths, rootName = null) {
    if (!filePaths || filePaths.length === 0) {
        return 'No files provided';
    }

    const tree = buildTreeFromPaths(filePaths);

    // Auto-detect root name if not provided
    if (!rootName && filePaths.length > 0) {
        const firstPath = filePaths[0];
        const parts = firstPath.split('/').filter(part => part.length > 0);
        rootName = parts.length > 0 ? parts[0] + '/' : 'root/';
    }

    return stringifyTreeWithRoot(tree, rootName);
}

/**
 * Stringify tree with custom root name
 * @param {Object} tree - Tree structure object
 * @param {string} rootName - Root directory name
 * @returns {string} Tree structure as string
 */
function stringifyTreeWithRoot(tree, rootName) {
    let result = rootName + '\n';
    const entries = Object.keys(tree).sort();

    // If there's only one root directory, skip it and show its children
    if (entries.length === 1 && tree[entries[0]].isDirectory) {
        const rootDir = entries[0];
        const children = tree[rootDir].children;
        result += stringifyTreeChildren(children, '');
    } else {
        result += stringifyTreeChildren(tree, '');
    }

    return result;
}

/**
 * Helper function to stringify tree children
 * @param {Object} children - Children object
 * @param {string} prefix - Current prefix
 * @returns {string} String representation
 */
function stringifyTreeChildren(children, prefix) {
    let result = '';
    const entries = Object.keys(children).sort();

    entries.forEach((name, index) => {
        const isLast = index === entries.length - 1;
        const node = children[name];

        const branchSymbol = isLast ? '└── ' : '├── ';
        const displayName = node.isDirectory ? `${name}/` : name;

        result += `${prefix}${branchSymbol}${displayName}\n`;

        if (node.isDirectory && Object.keys(node.children).length > 0) {
            const nextPrefix = prefix + (isLast ? '    ' : '│   ');
            result += stringifyTreeChildren(node.children, nextPrefix);
        }
    });

    return result;
}

// Example usage with your file paths
const filePaths = [
    'javascripts/service/dashboard/dashboard_service.ts',
    'javascripts/constants/cookies.ts',
    'javascripts/groups/DashboardGroupsFilter.vue',
    'javascripts/locales/en/dashboard/groups.js'
];

// Generate and display the tree
console.log('=== Example 1: Auto-detected root ===');
const treeOutput = generateFileTree(filePaths);
console.log(treeOutput);

console.log('=== Example 2: Custom root name ===');
const customRootOutput = generateFileTree(filePaths, '/path/to/project/');
console.log(customRootOutput);

console.log('=== Example 3: Different file structure ===');
const otherPaths = [
    'src/components/Header.jsx',
    'src/utils/helpers.js',
    'public/index.html',
    'package.json'
];
const otherTreeOutput = generateFileTree(otherPaths, 'my-project/');
console.log(otherTreeOutput);

// Export functions for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        generateFileTree,
        buildTreeFromPaths,
        stringifyTree,
        stringifyTreeWithRoot,
        stringifyTreeChildren
    };
}
