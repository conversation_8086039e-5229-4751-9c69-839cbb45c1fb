/**
 * Generate a tree structure from a list of file paths
 * Based on the computeAndStringifyDirectoryTree implementation
 */

/**
 * Build a tree structure from file paths
 * @param {string[]} filePaths - Array of file paths
 * @returns {Object} Tree structure object
 */
function buildTreeFromPaths(filePaths) {
    const tree = {};

    filePaths.forEach(filePath => {
        const parts = filePath.split('/').filter(part => part.length > 0);
        let current = tree;

        parts.forEach((part, index) => {
            if (!current[part]) {
                current[part] = {
                    isDirectory: index < parts.length - 1,
                    children: {}
                };
            }
            current = current[part].children;
        });
    });

    return tree;
}

/**
 * Convert tree structure to string representation
 * @param {Object} tree - Tree structure object
 * @param {string} prefix - Current prefix for tree formatting
 * @param {boolean} isRoot - Whether this is the root level
 * @param {boolean} showRootPath - Whether to show root path
 * @returns {string} String representation of the tree
 */
function stringifyTree(tree, prefix = '', isRoot = true, showRootPath = true) {
    let result = '';
    const entries = Object.keys(tree).sort();

    // Show root path only once at the very beginning
    if (isRoot && showRootPath) {
        result += 'javascripts/\n';
    }

    entries.forEach((name, index) => {
        const isLast = index === entries.length - 1;
        const node = tree[name];

        // Skip the root 'javascripts' directory in the tree structure
        if (isRoot && name === 'javascripts') {
            result += stringifyTree(node.children, '', false, false);
            return;
        }

        // Create the tree branch symbols
        const branchSymbol = isLast ? '└── ' : '├── ';
        const displayName = node.isDirectory ? `${name}/` : name;

        result += `${prefix}${branchSymbol}${displayName}\n`;

        // Process children if it's a directory
        if (node.isDirectory && Object.keys(node.children).length > 0) {
            const nextPrefix = prefix + (isLast ? '    ' : '│   ');
            result += stringifyTree(node.children, nextPrefix, false, false);
        }
    });

    return result;
}

/**
 * Determine the root path from the file paths
 * @param {string[]} entries - Directory entries
 * @param {Object} tree - Tree structure
 * @returns {string} Root path
 */
function getRootPath(entries, tree) {
    // Find common root directory
    if (entries.length === 0) return '';

    // For this example, we'll use a generic root
    return 'javascripts/';
}

/**
 * Main function to generate tree structure from file paths
 * @param {string[]} filePaths - Array of file paths
 * @param {string} rootName - Optional root directory name (auto-detected if not provided)
 * @returns {string} Tree structure as string
 */
function generateFileTree(filePaths, rootName = null) {
    if (!filePaths || filePaths.length === 0) {
        return 'No files provided';
    }

    const tree = buildTreeFromPaths(filePaths);

    // Auto-detect root name if not provided
    if (!rootName && filePaths.length > 0) {
        const firstPath = filePaths[0];
        const parts = firstPath.split('/').filter(part => part.length > 0);
        rootName = parts.length > 0 ? parts[0] + '/' : 'root/';
    }

    return stringifyTreeWithRoot(tree, rootName);
}

/**
 * Stringify tree with custom root name
 * @param {Object} tree - Tree structure object
 * @param {string} rootName - Root directory name
 * @returns {string} Tree structure as string
 */
function stringifyTreeWithRoot(tree, rootName) {
    let result = rootName + '\n';
    const entries = Object.keys(tree).sort();

    // If there's only one root directory, skip it and show its children
    if (entries.length === 1 && tree[entries[0]].isDirectory) {
        const rootDir = entries[0];
        const children = tree[rootDir].children;
        result += stringifyTreeChildren(children, '');
    } else {
        result += stringifyTreeChildren(tree, '');
    }

    return result;
}

/**
 * Check if a directory should be collapsed (has only one child and that child is also a directory)
 * @param {Object} node - Directory node
 * @returns {boolean} Whether the directory should be collapsed
 */
function shouldCollapseDirectory(node) {
    if (!node.isDirectory) return false;

    const childKeys = Object.keys(node.children);
    if (childKeys.length !== 1) return false;

    const onlyChild = node.children[childKeys[0]];
    return onlyChild.isDirectory;
}

/**
 * Get the collapsed path for a directory chain
 * @param {Object} node - Starting directory node
 * @param {string} currentPath - Current accumulated path
 * @returns {Object} Object with collapsedPath and finalNode
 */
function getCollapsedDirectoryPath(node, currentPath = '') {
    if (!node.isDirectory) {
        return { collapsedPath: currentPath, finalNode: node };
    }

    const childKeys = Object.keys(node.children);
    if (childKeys.length !== 1) {
        return { collapsedPath: currentPath, finalNode: node };
    }

    const childName = childKeys[0];
    const child = node.children[childName];
    const newPath = currentPath ? `${currentPath}/${childName}` : childName;

    if (child.isDirectory && shouldCollapseDirectory(child)) {
        return getCollapsedDirectoryPath(child, newPath);
    } else {
        return { collapsedPath: newPath, finalNode: child };
    }
}

/**
 * Helper function to stringify tree children with directory collapsing
 * @param {Object} children - Children object
 * @param {string} prefix - Current prefix
 * @returns {string} String representation
 */
function stringifyTreeChildren(children, prefix) {
    let result = '';
    const entries = Object.keys(children).sort();

    entries.forEach((name, index) => {
        const isLast = index === entries.length - 1;
        const node = children[name];

        const branchSymbol = isLast ? '└── ' : '├── ';

        // Check if this directory should be collapsed
        if (node.isDirectory && shouldCollapseDirectory(node)) {
            const { collapsedPath, finalNode } = getCollapsedDirectoryPath(node, name);
            const displayName = finalNode.isDirectory ? `${collapsedPath}/` : collapsedPath;

            result += `${prefix}${branchSymbol}${displayName}\n`;

            // If the final node is a directory with children, render them
            if (finalNode.isDirectory && Object.keys(finalNode.children).length > 0) {
                const nextPrefix = prefix + (isLast ? '    ' : '│   ');
                result += stringifyTreeChildren(finalNode.children, nextPrefix);
            }
        } else {
            // Normal rendering
            const displayName = node.isDirectory ? `${name}/` : name;
            result += `${prefix}${branchSymbol}${displayName}\n`;

            if (node.isDirectory && Object.keys(node.children).length > 0) {
                const nextPrefix = prefix + (isLast ? '    ' : '│   ');
                result += stringifyTreeChildren(node.children, nextPrefix);
            }
        }
    });

    return result;
}

// Example usage with your file paths
const filePaths = [
    'javascripts/service/dashboard/dashboard_service.ts',
    'javascripts/constants/cookies.ts',
    'javascripts/groups/DashboardGroupsFilter.vue',
    'javascripts/locales/en/dashboard/groups.js'
];

// Generate and display the tree
console.log('=== Example 1: 优化后的树形结构（合并单一路径目录）===');
const treeOutput = generateFileTree(filePaths);
console.log(treeOutput);

console.log('=== Example 2: Custom root name ===');
const customRootOutput = generateFileTree(filePaths, '/path/to/project/');
console.log(customRootOutput);

console.log('=== Example 3: 更复杂的目录结构测试 ===');
const complexPaths = [
    'src/components/ui/Button.jsx',
    'src/components/ui/Input.jsx',
    'src/utils/helpers/string.js',
    'src/utils/helpers/date.js',
    'src/pages/home/<USER>',
    'src/assets/images/logo.png',
    'src/deep/nested/single/path/file.txt',
    'public/favicon.ico',
    'package.json'
];
const complexTreeOutput = generateFileTree(complexPaths, 'my-project/');
console.log(complexTreeOutput);

// Export functions for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        generateFileTree,
        buildTreeFromPaths,
        stringifyTree,
        stringifyTreeWithRoot,
        stringifyTreeChildren
    };
}
