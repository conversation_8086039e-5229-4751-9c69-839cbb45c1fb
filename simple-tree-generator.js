/**
 * Simple file tree generator
 * Based on computeAndStringifyDirectoryTree implementation from VS Code
 */

/**
 * Generate a tree structure from file paths
 * @param {string[]} filePaths - Array of file paths
 * @param {string} rootName - Optional root directory name
 * @returns {string} Tree structure as string
 */
function generateFileTree(filePaths, rootName = null) {
    if (!filePaths || filePaths.length === 0) {
        return 'No files provided';
    }

    // Build tree structure
    const tree = {};
    filePaths.forEach(filePath => {
        const parts = filePath.split('/').filter(part => part.length > 0);
        let current = tree;

        parts.forEach((part, index) => {
            if (!current[part]) {
                current[part] = {
                    isDirectory: index < parts.length - 1,
                    children: {}
                };
            }
            current = current[part].children;
        });
    });

    // Auto-detect root name if not provided
    if (!rootName && filePaths.length > 0) {
        const firstPath = filePaths[0];
        const parts = firstPath.split('/').filter(part => part.length > 0);
        rootName = parts.length > 0 ? parts[0] + '/' : 'root/';
    }

    // Convert to string
    let result = rootName + '\n';
    const entries = Object.keys(tree).sort();

    // If there's only one root directory, skip it and show its children
    if (entries.length === 1 && tree[entries[0]].isDirectory) {
        const rootDir = entries[0];
        const children = tree[rootDir].children;
        result += renderChildren(children, '');
    } else {
        result += renderChildren(tree, '');
    }

    return result;
}

/**
 * Check if a directory should be collapsed (has only one child and that child is also a directory)
 * @param {Object} node - Directory node
 * @returns {boolean} Whether the directory should be collapsed
 */
function shouldCollapse(node) {
    if (!node.isDirectory) return false;

    const childKeys = Object.keys(node.children);
    if (childKeys.length !== 1) return false;

    const onlyChild = node.children[childKeys[0]];
    return onlyChild.isDirectory;
}

/**
 * Get the collapsed path for a directory chain
 * @param {Object} node - Starting directory node
 * @param {string} currentPath - Current accumulated path
 * @returns {Object} Object with collapsedPath and finalNode
 */
function getCollapsedPath(node, currentPath = '') {
    if (!node.isDirectory) {
        return { collapsedPath: currentPath, finalNode: node };
    }

    const childKeys = Object.keys(node.children);
    if (childKeys.length !== 1) {
        return { collapsedPath: currentPath, finalNode: node };
    }

    const childName = childKeys[0];
    const child = node.children[childName];
    const newPath = currentPath ? `${currentPath}/${childName}` : childName;

    if (child.isDirectory && shouldCollapse(child)) {
        return getCollapsedPath(child, newPath);
    } else {
        return { collapsedPath: newPath, finalNode: child };
    }
}

/**
 * Render children with tree formatting and directory collapsing
 * @param {Object} children - Children object
 * @param {string} prefix - Current prefix
 * @returns {string} Formatted string
 */
function renderChildren(children, prefix) {
    let result = '';
    const entries = Object.keys(children).sort();

    entries.forEach((name, index) => {
        const isLast = index === entries.length - 1;
        const node = children[name];

        const branchSymbol = isLast ? '└── ' : '├── ';

        // Check if this directory should be collapsed
        if (node.isDirectory && shouldCollapse(node)) {
            const { collapsedPath, finalNode } = getCollapsedPath(node, name);
            const displayName = finalNode.isDirectory ? `${collapsedPath}/` : collapsedPath;

            result += `${prefix}${branchSymbol}${displayName}\n`;

            // If the final node is a directory with children, render them
            if (finalNode.isDirectory && Object.keys(finalNode.children).length > 0) {
                const nextPrefix = prefix + (isLast ? '    ' : '│   ');
                result += renderChildren(finalNode.children, nextPrefix);
            }
        } else {
            // Normal rendering
            const displayName = node.isDirectory ? `${name}/` : name;
            result += `${prefix}${branchSymbol}${displayName}\n`;

            if (node.isDirectory && Object.keys(node.children).length > 0) {
                const nextPrefix = prefix + (isLast ? '    ' : '│   ');
                result += renderChildren(node.children, nextPrefix);
            }
        }
    });

    return result;
}

// Your specific file paths
const yourFilePaths = [
    'javascripts/service/dashboard/dashboard_service.ts',
    'javascripts/constants/cookies.ts',
    'javascripts/groups/DashboardGroupsFilter.vue',
    'javascripts/locales/en/dashboard/groups.js'
];

console.log('=== 优化后的树形结构（合并单一路径目录）===');
console.log(generateFileTree(yourFilePaths));

console.log('=== 更多测试用例 ===');
const testPaths = [
    'src/components/ui/Button.jsx',
    'src/utils/helpers/string.js',
    'src/utils/helpers/date.js',
    'src/pages/home/<USER>',
    'src/assets/images/logo.png',
    'public/favicon.ico',
    'package.json'
];

console.log(generateFileTree(testPaths, 'my-project/'));

// Export for use as module
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { generateFileTree };
}
