/**
 * Simple file tree generator
 * Based on computeAndStringifyDirectoryTree implementation from VS Code
 */

/**
 * Generate a tree structure from file paths
 * @param {string[]} filePaths - Array of file paths
 * @param {string} rootName - Optional root directory name
 * @returns {string} Tree structure as string
 */
function generateFileTree(filePaths, rootName = null) {
    if (!filePaths || filePaths.length === 0) {
        return 'No files provided';
    }

    // Build tree structure
    const tree = {};
    filePaths.forEach(filePath => {
        const parts = filePath.split('/').filter(part => part.length > 0);
        let current = tree;
        
        parts.forEach((part, index) => {
            if (!current[part]) {
                current[part] = {
                    isDirectory: index < parts.length - 1,
                    children: {}
                };
            }
            current = current[part].children;
        });
    });

    // Auto-detect root name if not provided
    if (!rootName && filePaths.length > 0) {
        const firstPath = filePaths[0];
        const parts = firstPath.split('/').filter(part => part.length > 0);
        rootName = parts.length > 0 ? parts[0] + '/' : 'root/';
    }

    // Convert to string
    let result = rootName + '\n';
    const entries = Object.keys(tree).sort();
    
    // If there's only one root directory, skip it and show its children
    if (entries.length === 1 && tree[entries[0]].isDirectory) {
        const rootDir = entries[0];
        const children = tree[rootDir].children;
        result += renderChildren(children, '');
    } else {
        result += renderChildren(tree, '');
    }
    
    return result;
}

/**
 * Render children with tree formatting
 * @param {Object} children - Children object
 * @param {string} prefix - Current prefix
 * @returns {string} Formatted string
 */
function renderChildren(children, prefix) {
    let result = '';
    const entries = Object.keys(children).sort();
    
    entries.forEach((name, index) => {
        const isLast = index === entries.length - 1;
        const node = children[name];
        
        const branchSymbol = isLast ? '└── ' : '├── ';
        const displayName = node.isDirectory ? `${name}/` : name;
        
        result += `${prefix}${branchSymbol}${displayName}\n`;
        
        if (node.isDirectory && Object.keys(node.children).length > 0) {
            const nextPrefix = prefix + (isLast ? '    ' : '│   ');
            result += renderChildren(node.children, nextPrefix);
        }
    });
    
    return result;
}

// Your specific file paths
const yourFilePaths = [
    'javascripts/service/dashboard/dashboard_service.ts',
    'javascripts/constants/cookies.ts',
    'javascripts/groups/DashboardGroupsFilter.vue',
    'javascripts/locales/en/dashboard/groups.js'
];

// Generate the tree
console.log(generateFileTree(yourFilePaths));

// Export for use as module
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { generateFileTree };
}
